# 🚀 Element-Plus-X Development Roadmap

## 📅 Short-term Plans (2025-Q3)

### 🎯 Goal: v1.3.X Version

- MD component marketplace project, enhancing generative AI rendering development
- Mini-program + workflow orchestration components. Coming soon, stay tuned...

## 🌱 Long-term Plans

### 1. Ecosystem Expansion Matrix

| Domain                                 | Planning Direction                                                                    | Timeline             |
| -------------------------------------- | ------------------------------------------------------------------------------------- | -------------------- |
| **Theme System**                       | Enrich Element-Plus-X component library with more themes                              | Q3 2025              |
| **MD Rendering Component Marketplace** | Create an MD component marketplace based on the newly launched MD components          | Q3 2025              |
| **Multi-platform Adaptation**          | Mini-program/Uniapp version development based on Vant UI style                        | Q4 2025              |
| **AI Component Library**               | Provide and expand more beautiful AI components for more usage scenarios              | Continuous iteration |
| **Solutions**                          | React/Vue full-stack Admin templates (with Ruoyi AI support)                          | Q3 2025              |
| **Workflow Orchestration Nodes**       | Develop lightweight open-source workflow orchestration node components to support MCP | Q4 2025              |
| **Toolchain**                          | Develop AI development assistance toolset, frontend MCP                               | Long-term planning   |
| **Frontier Exploration**               | 3D+AI technology integration prototype validation                                     | Long-term planning   |

### 2. Community Building

- Welcome more friends to exchange technology, learn and share cutting-edge technology together.
- Help each other, on issues, if you encounter problems during use, you can exchange usage experiences, share and grow your knowledge.
