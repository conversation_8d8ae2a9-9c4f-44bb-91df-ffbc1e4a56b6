# 🚀 Element-Plus-X 开发路线图

## 📅 近期计划（2025-Q3）

### 🎯 目标：v1.3.X 版本

- md 组件小广场项目，增强生成式AI渲染开发
- 小程序端+工作流编排组件。将提上日程 敬请期待...

## 🌱 长期计划

### 1. 生态扩展矩阵

| 领域               | 规划方向                                         | 时间节点 |
| ------------------ | ------------------------------------------------ | -------- |
| **主题系统**       | 为 Element-Plus-X 组件库丰富更多的主题           | Q3 2025  |
| **md渲染组件广场** | 基于新推出的 md 组创建一个 md 组件广场           | Q3 2025  |
| **多端适配**       | 小程序/Uniapp版本开发/基于 Vant UI风格           | Q4 2025  |
| **AI组件库**       | 提供、拓展更多好看的 AI 组件，应对更多的使用场景 | 持续迭代 |
| **解决方案**       | React/Vue全栈 Admin 模板（含Ruoyi AI支持）       | Q3 2025  |
| **工作流编排节点** | 开发轻量开源的工作流编排节点组件，助力 MCP       | Q4 2025  |
| **工具链**         | 开发AI开发辅助工具集，前端 MCP                   | 长期规划 |
| **前沿探索**       | 3D+AI技术融合原型验证                            | 长期规划 |

### 2. 社区建设

- 欢迎更多的朋友在一起交流技术，学习分享前沿技术。
- 相互帮助，在 issue 上，有使用中遇到的问题，可以相互交流使用经验，分享并增长你的见识。
