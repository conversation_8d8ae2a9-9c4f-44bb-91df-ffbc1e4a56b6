{"$schema": "./node_modules/oxlint/configuration_schema.json", "plugins": ["node", "jsdoc", "import", "unicorn"], "categories": {"correctness": "off"}, "env": {"builtin": true, "commonjs": true, "es2024": true, "node": true, "shared-node-browser": true}, "ignorePatterns": ["**/node_modules", "**/.pnp", "**/.pnp.js", "**/.env", "**/.env.local", "**/.env.development.local", "**/.env.test.local", "**/.env.production.local", "**/coverage", "**/.turbo", "**/.vercel", "**/.next/", "**/out/", "**/build", "**/dist", "**/npm-debug.log*", "**/yarn-debug.log*", "**/yarn-error.log*", "**/.DS_Store", "**/*.pem", "*/**/.vitepress/dist", "*/**/.vitepress/cache", "**/.idea", "**/*storybook.log", "**/package-lock.json", "**/yarn.lock", "**/pnpm-lock.yaml", "**/bun.lockb", "**/output", "**/temp", "**/.temp", "**/tmp", "**/.tmp", "**/.history", "**/.vitepress/cache", "**/.nuxt", "**/.next", "**/.svelte-kit", "**/.changeset", "**/.cache", "**/.output", "**/.vite-inspect", "**/.yarn", "**/vite.config.*.timestamp-*", "**/CHANGELOG*.md", "**/*.min.*", "**/LICENSE*", "**/__snapshots__", "**/auto-import?(s).d.ts", "**/components.d.ts", "**/dist/**", "**/storybook-static/**", "**/node_modules/**", "**/build/**", "**/lib/**", "**/es/**", "**/types/**", "**/public/**", "**/vite.config.ts", "**/eslint.config.js"], "globals": {"AbsoluteOrientationSensor": "readonly", "AbstractRange": "readonly", "Accelerometer": "readonly", "addEventListener": "readonly", "ai": "readonly", "AI": "readonly", "AICreateMonitor": "readonly", "AITextSession": "readonly", "alert": "readonly", "AnalyserNode": "readonly", "Animation": "readonly", "AnimationEffect": "readonly", "AnimationEvent": "readonly", "AnimationPlaybackEvent": "readonly", "AnimationTimeline": "readonly", "AsyncDisposableStack": "readonly", "Attr": "readonly", "Audio": "readonly", "AudioBuffer": "readonly", "AudioBufferSourceNode": "readonly", "AudioContext": "readonly", "AudioData": "readonly", "AudioDecoder": "readonly", "AudioDestinationNode": "readonly", "AudioEncoder": "readonly", "AudioListener": "readonly", "AudioNode": "readonly", "AudioParam": "readonly", "AudioParamMap": "readonly", "AudioProcessingEvent": "readonly", "AudioScheduledSourceNode": "readonly", "AudioSinkInfo": "readonly", "AudioWorklet": "readonly", "AudioWorkletGlobalScope": "readonly", "AudioWorkletNode": "readonly", "AudioWorkletProcessor": "readonly", "AuthenticatorAssertionResponse": "readonly", "AuthenticatorAttestationResponse": "readonly", "AuthenticatorResponse": "readonly", "BackgroundFetchManager": "readonly", "BackgroundFetchRecord": "readonly", "BackgroundFetchRegistration": "readonly", "BarcodeDetector": "readonly", "BarProp": "readonly", "BaseAudioContext": "readonly", "BatteryManager": "readonly", "BeforeUnloadEvent": "readonly", "BiquadFilterNode": "readonly", "BlobEvent": "readonly", "Bluetooth": "readonly", "BluetoothCharacteristicProperties": "readonly", "BluetoothDevice": "readonly", "BluetoothRemoteGATTCharacteristic": "readonly", "BluetoothRemoteGATTDescriptor": "readonly", "BluetoothRemoteGATTServer": "readonly", "BluetoothRemoteGATTService": "readonly", "BluetoothUUID": "readonly", "blur": "readonly", "BrowserCaptureMediaStreamTrack": "readonly", "Cache": "readonly", "caches": "readonly", "CacheStorage": "readonly", "cancelAnimationFrame": "readonly", "cancelIdleCallback": "readonly", "CanvasCaptureMediaStream": "readonly", "CanvasCaptureMediaStreamTrack": "readonly", "CanvasGradient": "readonly", "CanvasPattern": "readonly", "CanvasRenderingContext2D": "readonly", "CaptureController": "readonly", "CaretPosition": "readonly", "CDATASection": "readonly", "ChannelMergerNode": "readonly", "ChannelSplitterNode": "readonly", "ChapterInformation": "readonly", "CharacterBoundsUpdateEvent": "readonly", "CharacterData": "readonly", "clientInformation": "readonly", "Clipboard": "readonly", "ClipboardEvent": "readonly", "ClipboardItem": "readonly", "close": "readonly", "closed": "readonly", "CloseWatcher": "readonly", "CommandEvent": "readonly", "Comment": "readonly", "CompositionEvent": "readonly", "confirm": "readonly", "ConstantSourceNode": "readonly", "ContentVisibilityAutoStateChangeEvent": "readonly", "ConvolverNode": "readonly", "CookieChangeEvent": "readonly", "CookieDeprecationLabel": "readonly", "cookieStore": "readonly", "CookieStore": "readonly", "CookieStoreManager": "readonly", "createImageBitmap": "readonly", "Credential": "readonly", "credentialless": "readonly", "CredentialsContainer": "readonly", "CropTarget": "readonly", "crossOriginIsolated": "readonly", "CSPViolationReportBody": "readonly", "CSS": "readonly", "CSSAnimation": "readonly", "CSSConditionRule": "readonly", "CSSContainerRule": "readonly", "CSSCounterStyleRule": "readonly", "CSSFontFaceRule": "readonly", "CSSFontFeatureValuesRule": "readonly", "CSSFontPaletteValuesRule": "readonly", "CSSGroupingRule": "readonly", "CSSImageValue": "readonly", "CSSImportRule": "readonly", "CSSKeyframeRule": "readonly", "CSSKeyframesRule": "readonly", "CSSKeywordValue": "readonly", "CSSLayerBlockRule": "readonly", "CSSLayerStatementRule": "readonly", "CSSMarginRule": "readonly", "CSSMathClamp": "readonly", "CSSMathInvert": "readonly", "CSSMathMax": "readonly", "CSSMathMin": "readonly", "CSSMathNegate": "readonly", "CSSMathProduct": "readonly", "CSSMathSum": "readonly", "CSSMathValue": "readonly", "CSSMatrixComponent": "readonly", "CSSMediaRule": "readonly", "CSSNamespaceRule": "readonly", "CSSNestedDeclarations": "readonly", "CSSNumericArray": "readonly", "CSSNumericValue": "readonly", "CSSPageDescriptors": "readonly", "CSSPageRule": "readonly", "CSSPerspective": "readonly", "CSSPositionTryDescriptors": "readonly", "CSSPositionTryRule": "readonly", "CSSPositionValue": "readonly", "CSSPropertyRule": "readonly", "CSSRotate": "readonly", "CSSRule": "readonly", "CSSRuleList": "readonly", "CSSScale": "readonly", "CSSScopeRule": "readonly", "CSSSkew": "readonly", "CSSSkewX": "readonly", "CSSSkewY": "readonly", "CSSStartingStyleRule": "readonly", "CSSStyleDeclaration": "readonly", "CSSStyleRule": "readonly", "CSSStyleSheet": "readonly", "CSSStyleValue": "readonly", "CSSSupportsRule": "readonly", "CSSTransformComponent": "readonly", "CSSTransformValue": "readonly", "CSSTransition": "readonly", "CSSTranslate": "readonly", "CSSUnitValue": "readonly", "CSSUnparsedValue": "readonly", "CSSVariableReferenceValue": "readonly", "CSSViewTransitionRule": "readonly", "currentFrame": "readonly", "currentTime": "readonly", "CustomElementRegistry": "readonly", "customElements": "readonly", "CustomStateSet": "readonly", "DataTransfer": "readonly", "DataTransferItem": "readonly", "DataTransferItemList": "readonly", "DelayNode": "readonly", "DelegatedInkTrailPresenter": "readonly", "DeviceMotionEvent": "readonly", "DeviceMotionEventAcceleration": "readonly", "DeviceMotionEventRotationRate": "readonly", "DeviceOrientationEvent": "readonly", "devicePixelRatio": "readonly", "DevicePosture": "readonly", "dispatchEvent": "readonly", "DisposableStack": "readonly", "document": "readonly", "Document": "readonly", "DocumentFragment": "readonly", "documentPictureInPicture": "readonly", "DocumentPictureInPicture": "readonly", "DocumentPictureInPictureEvent": "readonly", "DocumentTimeline": "readonly", "DocumentType": "readonly", "DOMError": "readonly", "DOMImplementation": "readonly", "DOMMatrix": "readonly", "DOMMatrixReadOnly": "readonly", "DOMParser": "readonly", "DOMPoint": "readonly", "DOMPointReadOnly": "readonly", "DOMQuad": "readonly", "DOMRect": "readonly", "DOMRectList": "readonly", "DOMRectReadOnly": "readonly", "DOMStringList": "readonly", "DOMStringMap": "readonly", "DOMTokenList": "readonly", "DragEvent": "readonly", "DynamicsCompressorNode": "readonly", "EditContext": "readonly", "Element": "readonly", "ElementInternals": "readonly", "EncodedAudioChunk": "readonly", "EncodedVideoChunk": "readonly", "ErrorEvent": "readonly", "event": "readonly", "EventCounts": "readonly", "EventSource": "readonly", "external": "readonly", "External": "readonly", "EyeDropper": "readonly", "FeaturePolicy": "readonly", "FederatedCredential": "readonly", "fence": "readonly", "Fence": "readonly", "FencedFrameConfig": "readonly", "fetchLater": "readonly", "FetchLaterResult": "readonly", "FileList": "readonly", "FileReader": "readonly", "FileSystem": "readonly", "FileSystemDirectoryEntry": "readonly", "FileSystemDirectoryHandle": "readonly", "FileSystemDirectoryReader": "readonly", "FileSystemEntry": "readonly", "FileSystemFileEntry": "readonly", "FileSystemFileHandle": "readonly", "FileSystemHandle": "readonly", "FileSystemObserver": "readonly", "FileSystemWritableFileStream": "readonly", "find": "readonly", "focus": "readonly", "FocusEvent": "readonly", "FontData": "readonly", "FontFace": "readonly", "FontFaceSet": "readonly", "FontFaceSetLoadEvent": "readonly", "FormDataEvent": "readonly", "FragmentDirective": "readonly", "frameElement": "readonly", "frames": "readonly", "GainNode": "readonly", "Gamepad": "readonly", "GamepadAxisMoveEvent": "readonly", "GamepadButton": "readonly", "GamepadButtonEvent": "readonly", "GamepadEvent": "readonly", "GamepadHapticActuator": "readonly", "GamepadPose": "readonly", "Geolocation": "readonly", "GeolocationCoordinates": "readonly", "GeolocationPosition": "readonly", "GeolocationPositionError": "readonly", "getComputedStyle": "readonly", "getScreenDetails": "readonly", "getSelection": "readonly", "GPU": "readonly", "GPUAdapter": "readonly", "GPUAdapterInfo": "readonly", "GPUBindGroup": "readonly", "GPUBindGroupLayout": "readonly", "GPUBuffer": "readonly", "GPUBufferUsage": "readonly", "GPUCanvasContext": "readonly", "GPUColorWrite": "readonly", "GPUCommandBuffer": "readonly", "GPUCommandEncoder": "readonly", "GPUCompilationInfo": "readonly", "GPUCompilationMessage": "readonly", "GPUComputePassEncoder": "readonly", "GPUComputePipeline": "readonly", "GPUDevice": "readonly", "GPUDeviceLostInfo": "readonly", "GPUError": "readonly", "GPUExternalTexture": "readonly", "GPUInternalError": "readonly", "GPUMapMode": "readonly", "GPUOutOfMemoryError": "readonly", "GPUPipelineError": "readonly", "GPUPipelineLayout": "readonly", "GPUQuerySet": "readonly", "GPUQueue": "readonly", "GPURenderBundle": "readonly", "GPURenderBundleEncoder": "readonly", "GPURenderPassEncoder": "readonly", "GPURenderPipeline": "readonly", "GPUSampler": "readonly", "GPUShaderModule": "readonly", "GPUShaderStage": "readonly", "GPUSupportedFeatures": "readonly", "GPUSupportedLimits": "readonly", "GPUTexture": "readonly", "GPUTextureUsage": "readonly", "GPUTextureView": "readonly", "GPUUncapturedErrorEvent": "readonly", "GPUValidationError": "readonly", "GravitySensor": "readonly", "Gyroscope": "readonly", "HashChangeEvent": "readonly", "HID": "readonly", "HIDConnectionEvent": "readonly", "HIDDevice": "readonly", "HIDInputReportEvent": "readonly", "Highlight": "readonly", "HighlightRegistry": "readonly", "history": "readonly", "History": "readonly", "HTMLAllCollection": "readonly", "HTMLAnchorElement": "readonly", "HTMLAreaElement": "readonly", "HTMLAudioElement": "readonly", "HTMLBaseElement": "readonly", "HTMLBodyElement": "readonly", "HTMLBRElement": "readonly", "HTMLButtonElement": "readonly", "HTMLCanvasElement": "readonly", "HTMLCollection": "readonly", "HTMLDataElement": "readonly", "HTMLDataListElement": "readonly", "HTMLDetailsElement": "readonly", "HTMLDialogElement": "readonly", "HTMLDirectoryElement": "readonly", "HTMLDivElement": "readonly", "HTMLDListElement": "readonly", "HTMLDocument": "readonly", "HTMLElement": "readonly", "HTMLEmbedElement": "readonly", "HTMLFencedFrameElement": "readonly", "HTMLFieldSetElement": "readonly", "HTMLFontElement": "readonly", "HTMLFormControlsCollection": "readonly", "HTMLFormElement": "readonly", "HTMLFrameElement": "readonly", "HTMLFrameSetElement": "readonly", "HTMLHeadElement": "readonly", "HTMLHeadingElement": "readonly", "HTMLHRElement": "readonly", "HTMLHtmlElement": "readonly", "HTMLIFrameElement": "readonly", "HTMLImageElement": "readonly", "HTMLInputElement": "readonly", "HTMLLabelElement": "readonly", "HTMLLegendElement": "readonly", "HTMLLIElement": "readonly", "HTMLLinkElement": "readonly", "HTMLMapElement": "readonly", "HTMLMarqueeElement": "readonly", "HTMLMediaElement": "readonly", "HTMLMenuElement": "readonly", "HTMLMetaElement": "readonly", "HTMLMeterElement": "readonly", "HTMLModElement": "readonly", "HTMLObjectElement": "readonly", "HTMLOListElement": "readonly", "HTMLOptGroupElement": "readonly", "HTMLOptionElement": "readonly", "HTMLOptionsCollection": "readonly", "HTMLOutputElement": "readonly", "HTMLParagraphElement": "readonly", "HTMLParamElement": "readonly", "HTMLPictureElement": "readonly", "HTMLPreElement": "readonly", "HTMLProgressElement": "readonly", "HTMLQuoteElement": "readonly", "HTMLScriptElement": "readonly", "HTMLSelectedContentElement": "readonly", "HTMLSelectElement": "readonly", "HTMLSlotElement": "readonly", "HTMLSourceElement": "readonly", "HTMLSpanElement": "readonly", "HTMLStyleElement": "readonly", "HTMLTableCaptionElement": "readonly", "HTMLTableCellElement": "readonly", "HTMLTableColElement": "readonly", "HTMLTableElement": "readonly", "HTMLTableRowElement": "readonly", "HTMLTableSectionElement": "readonly", "HTMLTemplateElement": "readonly", "HTMLTextAreaElement": "readonly", "HTMLTimeElement": "readonly", "HTMLTitleElement": "readonly", "HTMLTrackElement": "readonly", "HTMLUListElement": "readonly", "HTMLUnknownElement": "readonly", "HTMLVideoElement": "readonly", "IDBCursor": "readonly", "IDBCursorWithValue": "readonly", "IDBDatabase": "readonly", "IDBFactory": "readonly", "IDBIndex": "readonly", "IDBKeyRange": "readonly", "IDBObjectStore": "readonly", "IDBOpenDBRequest": "readonly", "IDBRequest": "readonly", "IDBTransaction": "readonly", "IDBVersionChangeEvent": "readonly", "IdentityCredential": "readonly", "IdentityCredentialError": "readonly", "IdentityProvider": "readonly", "IdleDeadline": "readonly", "IdleDetector": "readonly", "IIRFilterNode": "readonly", "Image": "readonly", "ImageBitmap": "readonly", "ImageBitmapRenderingContext": "readonly", "ImageCapture": "readonly", "ImageData": "readonly", "ImageDecoder": "readonly", "ImageTrack": "readonly", "ImageTrackList": "readonly", "indexedDB": "readonly", "Ink": "readonly", "innerHeight": "readonly", "innerWidth": "readonly", "InputDeviceCapabilities": "readonly", "InputDeviceInfo": "readonly", "InputEvent": "readonly", "IntersectionObserver": "readonly", "IntersectionObserverEntry": "readonly", "isSecureContext": "readonly", "Keyboard": "readonly", "KeyboardEvent": "readonly", "KeyboardLayoutMap": "readonly", "KeyframeEffect": "readonly", "LanguageDetector": "readonly", "LargestContentfulPaint": "readonly", "LaunchParams": "readonly", "launchQueue": "readonly", "LaunchQueue": "readonly", "LayoutShift": "readonly", "LayoutShiftAttribution": "readonly", "length": "readonly", "LinearAccelerationSensor": "readonly", "localStorage": "readonly", "location": "writeable", "Location": "readonly", "locationbar": "readonly", "Lock": "readonly", "LockManager": "readonly", "matchMedia": "readonly", "MathMLElement": "readonly", "MediaCapabilities": "readonly", "MediaCapabilitiesInfo": "readonly", "MediaDeviceInfo": "readonly", "MediaDevices": "readonly", "MediaElementAudioSourceNode": "readonly", "MediaEncryptedEvent": "readonly", "MediaError": "readonly", "MediaKeyError": "readonly", "MediaKeyMessageEvent": "readonly", "MediaKeys": "readonly", "MediaKeySession": "readonly", "MediaKeyStatusMap": "readonly", "MediaKeySystemAccess": "readonly", "MediaList": "readonly", "MediaMetadata": "readonly", "MediaQueryList": "readonly", "MediaQueryListEvent": "readonly", "MediaRecorder": "readonly", "MediaRecorderErrorEvent": "readonly", "MediaSession": "readonly", "MediaSource": "readonly", "MediaSourceHandle": "readonly", "MediaStream": "readonly", "MediaStreamAudioDestinationNode": "readonly", "MediaStreamAudioSourceNode": "readonly", "MediaStreamEvent": "readonly", "MediaStreamTrack": "readonly", "MediaStreamTrackAudioSourceNode": "readonly", "MediaStreamTrackAudioStats": "readonly", "MediaStreamTrackEvent": "readonly", "MediaStreamTrackGenerator": "readonly", "MediaStreamTrackProcessor": "readonly", "MediaStreamTrackVideoStats": "readonly", "menubar": "readonly", "MIDIAccess": "readonly", "MIDIConnectionEvent": "readonly", "MIDIInput": "readonly", "MIDIInputMap": "readonly", "MIDIMessageEvent": "readonly", "MIDIOutput": "readonly", "MIDIOutputMap": "readonly", "MIDIPort": "readonly", "MimeType": "readonly", "MimeTypeArray": "readonly", "model": "readonly", "ModelGenericSession": "readonly", "ModelManager": "readonly", "MouseEvent": "readonly", "moveBy": "readonly", "moveTo": "readonly", "MutationEvent": "readonly", "MutationObserver": "readonly", "MutationRecord": "readonly", "name": "readonly", "NamedNodeMap": "readonly", "NavigateEvent": "readonly", "navigation": "readonly", "Navigation": "readonly", "NavigationActivation": "readonly", "NavigationCurrentEntryChangeEvent": "readonly", "NavigationDestination": "readonly", "NavigationHistoryEntry": "readonly", "NavigationPreloadManager": "readonly", "NavigationTransition": "readonly", "NavigatorLogin": "readonly", "NavigatorManagedData": "readonly", "NavigatorUAData": "readonly", "NetworkInformation": "readonly", "Node": "readonly", "NodeFilter": "readonly", "NodeIterator": "readonly", "NodeList": "readonly", "Notification": "readonly", "NotifyPaintEvent": "readonly", "NotRestoredReasonDetails": "readonly", "NotRestoredReasons": "readonly", "Observable": "readonly", "OfflineAudioCompletionEvent": "readonly", "OfflineAudioContext": "readonly", "offscreenBuffering": "readonly", "OffscreenCanvas": "readonly", "OffscreenCanvasRenderingContext2D": "readonly", "onabort": "writeable", "onafterprint": "writeable", "onanimationcancel": "writeable", "onanimationend": "writeable", "onanimationiteration": "writeable", "onanimationstart": "writeable", "onappinstalled": "writeable", "onauxclick": "writeable", "onbeforeinput": "writeable", "onbeforeinstallprompt": "writeable", "onbeforematch": "writeable", "onbeforeprint": "writeable", "onbeforetoggle": "writeable", "onbeforeunload": "writeable", "onbeforexrselect": "writeable", "onblur": "writeable", "oncancel": "writeable", "oncanplay": "writeable", "oncanplaythrough": "writeable", "onchange": "writeable", "onclick": "writeable", "onclose": "writeable", "oncommand": "writeable", "oncontentvisibilityautostatechange": "writeable", "oncontextlost": "writeable", "oncontextmenu": "writeable", "oncontextrestored": "writeable", "oncopy": "writeable", "oncuechange": "writeable", "oncut": "writeable", "ondblclick": "writeable", "ondevicemotion": "writeable", "ondeviceorientation": "writeable", "ondeviceorientationabsolute": "writeable", "ondrag": "writeable", "ondragend": "writeable", "ondragenter": "writeable", "ondragleave": "writeable", "ondragover": "writeable", "ondragstart": "writeable", "ondrop": "writeable", "ondurationchange": "writeable", "onemptied": "writeable", "onended": "writeable", "onerror": "writeable", "onfocus": "writeable", "onformdata": "writeable", "ongamepadconnected": "writeable", "ongamepaddisconnected": "writeable", "ongotpointercapture": "writeable", "onhashchange": "writeable", "oninput": "writeable", "oninvalid": "writeable", "onkeydown": "writeable", "onkeypress": "writeable", "onkeyup": "writeable", "onlanguagechange": "writeable", "onload": "writeable", "onloadeddata": "writeable", "onloadedmetadata": "writeable", "onloadstart": "writeable", "onlostpointercapture": "writeable", "onmessage": "writeable", "onmessageerror": "writeable", "onmousedown": "writeable", "onmouseenter": "writeable", "onmouseleave": "writeable", "onmousemove": "writeable", "onmouseout": "writeable", "onmouseover": "writeable", "onmouseup": "writeable", "onmousewheel": "writeable", "onoffline": "writeable", "ononline": "writeable", "onpagehide": "writeable", "onpagereveal": "writeable", "onpageshow": "writeable", "onpageswap": "writeable", "onpaste": "writeable", "onpause": "writeable", "onplay": "writeable", "onplaying": "writeable", "onpointercancel": "writeable", "onpointerdown": "writeable", "onpointerenter": "writeable", "onpointerleave": "writeable", "onpointermove": "writeable", "onpointerout": "writeable", "onpointerover": "writeable", "onpointerrawupdate": "writeable", "onpointerup": "writeable", "onpopstate": "writeable", "onprogress": "writeable", "onratechange": "writeable", "onrejectionhandled": "writeable", "onreset": "writeable", "onresize": "writeable", "onscroll": "writeable", "onscrollend": "writeable", "onscrollsnapchange": "writeable", "onscrollsnapchanging": "writeable", "onsearch": "writeable", "onsecuritypolicyviolation": "writeable", "onseeked": "writeable", "onseeking": "writeable", "onselect": "writeable", "onselectionchange": "writeable", "onselectstart": "writeable", "onslotchange": "writeable", "onstalled": "writeable", "onstorage": "writeable", "onsubmit": "writeable", "onsuspend": "writeable", "ontimeupdate": "writeable", "ontoggle": "writeable", "ontransitioncancel": "writeable", "ontransitionend": "writeable", "ontransitionrun": "writeable", "ontransitionstart": "writeable", "onunhandledrejection": "writeable", "onunload": "writeable", "onvolumechange": "writeable", "onwaiting": "writeable", "onwheel": "writeable", "open": "readonly", "opener": "readonly", "Option": "readonly", "OrientationSensor": "readonly", "origin": "readonly", "originAgentCluster": "readonly", "OscillatorNode": "readonly", "OTPCredential": "readonly", "outerHeight": "readonly", "outerWidth": "readonly", "OverconstrainedError": "readonly", "PageRevealEvent": "readonly", "PageSwapEvent": "readonly", "PageTransitionEvent": "readonly", "pageXOffset": "readonly", "pageYOffset": "readonly", "PannerNode": "readonly", "parent": "readonly", "PasswordCredential": "readonly", "Path2D": "readonly", "PaymentAddress": "readonly", "PaymentManager": "readonly", "PaymentMethodChangeEvent": "readonly", "PaymentRequest": "readonly", "PaymentRequestUpdateEvent": "readonly", "PaymentResponse": "readonly", "PerformanceElementTiming": "readonly", "PerformanceEventTiming": "readonly", "PerformanceLongAnimationFrameTiming": "readonly", "PerformanceLongTaskTiming": "readonly", "PerformanceNavigation": "readonly", "PerformanceNavigationTiming": "readonly", "PerformancePaintTiming": "readonly", "PerformanceScriptTiming": "readonly", "PerformanceServerTiming": "readonly", "PerformanceTiming": "readonly", "PeriodicSyncManager": "readonly", "PeriodicWave": "readonly", "Permissions": "readonly", "PermissionStatus": "readonly", "PERSISTENT": "readonly", "personalbar": "readonly", "PictureInPictureEvent": "readonly", "PictureInPictureWindow": "readonly", "Plugin": "readonly", "PluginArray": "readonly", "PointerEvent": "readonly", "PopStateEvent": "readonly", "postMessage": "readonly", "Presentation": "readonly", "PresentationAvailability": "readonly", "PresentationConnection": "readonly", "PresentationConnectionAvailableEvent": "readonly", "PresentationConnectionCloseEvent": "readonly", "PresentationConnectionList": "readonly", "PresentationReceiver": "readonly", "PresentationRequest": "readonly", "PressureObserver": "readonly", "PressureRecord": "readonly", "print": "readonly", "ProcessingInstruction": "readonly", "Profiler": "readonly", "ProgressEvent": "readonly", "PromiseRejectionEvent": "readonly", "prompt": "readonly", "ProtectedAudience": "readonly", "PublicKeyCredential": "readonly", "PushManager": "readonly", "PushSubscription": "readonly", "PushSubscriptionOptions": "readonly", "queryLocalFonts": "readonly", "RadioNodeList": "readonly", "Range": "readonly", "registerProcessor": "readonly", "RelativeOrientationSensor": "readonly", "RemotePlayback": "readonly", "removeEventListener": "readonly", "ReportBody": "readonly", "reportError": "readonly", "ReportingObserver": "readonly", "requestAnimationFrame": "readonly", "requestIdleCallback": "readonly", "resizeBy": "readonly", "ResizeObserver": "readonly", "ResizeObserverEntry": "readonly", "ResizeObserverSize": "readonly", "resizeTo": "readonly", "RestrictionTarget": "readonly", "RTCCertificate": "readonly", "RTCDataChannel": "readonly", "RTCDataChannelEvent": "readonly", "RTCDtlsTransport": "readonly", "RTCDTMFSender": "readonly", "RTCDTMFToneChangeEvent": "readonly", "RTCEncodedAudioFrame": "readonly", "RTCEncodedVideoFrame": "readonly", "RTCError": "readonly", "RTCErrorEvent": "readonly", "RTCIceCandidate": "readonly", "RTCIceTransport": "readonly", "RTCPeerConnection": "readonly", "RTCPeerConnectionIceErrorEvent": "readonly", "RTCPeerConnectionIceEvent": "readonly", "RTCRtpReceiver": "readonly", "RTCRtpScriptTransform": "readonly", "RTCRtpSender": "readonly", "RTCRtpTransceiver": "readonly", "RTCSctpTransport": "readonly", "RTCSessionDescription": "readonly", "RTCStatsReport": "readonly", "RTCTrackEvent": "readonly", "sampleRate": "readonly", "scheduler": "readonly", "Scheduler": "readonly", "Scheduling": "readonly", "screen": "readonly", "Screen": "readonly", "ScreenDetailed": "readonly", "ScreenDetails": "readonly", "screenLeft": "readonly", "ScreenOrientation": "readonly", "screenTop": "readonly", "screenX": "readonly", "screenY": "readonly", "ScriptProcessorNode": "readonly", "scroll": "readonly", "scrollbars": "readonly", "scrollBy": "readonly", "ScrollTimeline": "readonly", "scrollTo": "readonly", "scrollX": "readonly", "scrollY": "readonly", "SecurityPolicyViolationEvent": "readonly", "Selection": "readonly", "self": "readonly", "Sensor": "readonly", "SensorErrorEvent": "readonly", "Serial": "readonly", "SerialPort": "readonly", "ServiceWorker": "readonly", "ServiceWorkerContainer": "readonly", "ServiceWorkerRegistration": "readonly", "sessionStorage": "readonly", "ShadowRoot": "readonly", "sharedStorage": "readonly", "SharedStorage": "readonly", "SharedStorageAppendMethod": "readonly", "SharedStorageClearMethod": "readonly", "SharedStorageDeleteMethod": "readonly", "SharedStorageModifierMethod": "readonly", "SharedStorageSetMethod": "readonly", "SharedStorageWorklet": "readonly", "SharedWorker": "readonly", "showDirectoryPicker": "readonly", "showOpenFilePicker": "readonly", "showSaveFilePicker": "readonly", "SnapEvent": "readonly", "SourceBuffer": "readonly", "SourceBufferList": "readonly", "speechSynthesis": "readonly", "SpeechSynthesis": "readonly", "SpeechSynthesisErrorEvent": "readonly", "SpeechSynthesisEvent": "readonly", "SpeechSynthesisUtterance": "readonly", "SpeechSynthesisVoice": "readonly", "StaticRange": "readonly", "status": "readonly", "statusbar": "readonly", "StereoPannerNode": "readonly", "stop": "readonly", "Storage": "readonly", "StorageBucket": "readonly", "StorageBucketManager": "readonly", "StorageEvent": "readonly", "StorageManager": "readonly", "styleMedia": "readonly", "StylePropertyMap": "readonly", "StylePropertyMapReadOnly": "readonly", "StyleSheet": "readonly", "StyleSheetList": "readonly", "SubmitEvent": "readonly", "Subscriber": "readonly", "SuppressedError": "readonly", "SVGAElement": "readonly", "SVGAngle": "readonly", "SVGAnimatedAngle": "readonly", "SVGAnimatedBoolean": "readonly", "SVGAnimatedEnumeration": "readonly", "SVGAnimatedInteger": "readonly", "SVGAnimatedLength": "readonly", "SVGAnimatedLengthList": "readonly", "SVGAnimatedNumber": "readonly", "SVGAnimatedNumberList": "readonly", "SVGAnimatedPreserveAspectRatio": "readonly", "SVGAnimatedRect": "readonly", "SVGAnimatedString": "readonly", "SVGAnimatedTransformList": "readonly", "SVGAnimateElement": "readonly", "SVGAnimateMotionElement": "readonly", "SVGAnimateTransformElement": "readonly", "SVGAnimationElement": "readonly", "SVGCircleElement": "readonly", "SVGClipPathElement": "readonly", "SVGComponentTransferFunctionElement": "readonly", "SVGDefsElement": "readonly", "SVGDescElement": "readonly", "SVGElement": "readonly", "SVGEllipseElement": "readonly", "SVGFEBlendElement": "readonly", "SVGFEColorMatrixElement": "readonly", "SVGFEComponentTransferElement": "readonly", "SVGFECompositeElement": "readonly", "SVGFEConvolveMatrixElement": "readonly", "SVGFEDiffuseLightingElement": "readonly", "SVGFEDisplacementMapElement": "readonly", "SVGFEDistantLightElement": "readonly", "SVGFEDropShadowElement": "readonly", "SVGFEFloodElement": "readonly", "SVGFEFuncAElement": "readonly", "SVGFEFuncBElement": "readonly", "SVGFEFuncGElement": "readonly", "SVGFEFuncRElement": "readonly", "SVGFEGaussianBlurElement": "readonly", "SVGFEImageElement": "readonly", "SVGFEMergeElement": "readonly", "SVGFEMergeNodeElement": "readonly", "SVGFEMorphologyElement": "readonly", "SVGFEOffsetElement": "readonly", "SVGFEPointLightElement": "readonly", "SVGFESpecularLightingElement": "readonly", "SVGFESpotLightElement": "readonly", "SVGFETileElement": "readonly", "SVGFETurbulenceElement": "readonly", "SVGFilterElement": "readonly", "SVGForeignObjectElement": "readonly", "SVGGElement": "readonly", "SVGGeometryElement": "readonly", "SVGGradientElement": "readonly", "SVGGraphicsElement": "readonly", "SVGImageElement": "readonly", "SVGLength": "readonly", "SVGLengthList": "readonly", "SVGLinearGradientElement": "readonly", "SVGLineElement": "readonly", "SVGMarkerElement": "readonly", "SVGMaskElement": "readonly", "SVGMatrix": "readonly", "SVGMetadataElement": "readonly", "SVGMPathElement": "readonly", "SVGNumber": "readonly", "SVGNumberList": "readonly", "SVGPathElement": "readonly", "SVGPatternElement": "readonly", "SVGPoint": "readonly", "SVGPointList": "readonly", "SVGPolygonElement": "readonly", "SVGPolylineElement": "readonly", "SVGPreserveAspectRatio": "readonly", "SVGRadialGradientElement": "readonly", "SVGRect": "readonly", "SVGRectElement": "readonly", "SVGScriptElement": "readonly", "SVGSetElement": "readonly", "SVGStopElement": "readonly", "SVGStringList": "readonly", "SVGStyleElement": "readonly", "SVGSVGElement": "readonly", "SVGSwitchElement": "readonly", "SVGSymbolElement": "readonly", "SVGTextContentElement": "readonly", "SVGTextElement": "readonly", "SVGTextPathElement": "readonly", "SVGTextPositioningElement": "readonly", "SVGTitleElement": "readonly", "SVGTransform": "readonly", "SVGTransformList": "readonly", "SVGTSpanElement": "readonly", "SVGUnitTypes": "readonly", "SVGUseElement": "readonly", "SVGViewElement": "readonly", "SyncManager": "readonly", "TaskAttributionTiming": "readonly", "TaskController": "readonly", "TaskPriorityChangeEvent": "readonly", "TaskSignal": "readonly", "TEMPORARY": "readonly", "Text": "readonly", "TextEvent": "readonly", "TextFormat": "readonly", "TextFormatUpdateEvent": "readonly", "TextMetrics": "readonly", "TextTrack": "readonly", "TextTrackCue": "readonly", "TextTrackCueList": "readonly", "TextTrackList": "readonly", "TextUpdateEvent": "readonly", "TimeEvent": "readonly", "TimeRanges": "readonly", "ToggleEvent": "readonly", "toolbar": "readonly", "top": "readonly", "Touch": "readonly", "TouchEvent": "readonly", "TouchList": "readonly", "TrackEvent": "readonly", "TransitionEvent": "readonly", "TreeWalker": "readonly", "TrustedHTML": "readonly", "TrustedScript": "readonly", "TrustedScriptURL": "readonly", "TrustedTypePolicy": "readonly", "TrustedTypePolicyFactory": "readonly", "trustedTypes": "readonly", "UIEvent": "readonly", "URLPattern": "readonly", "USB": "readonly", "USBAlternateInterface": "readonly", "USBConfiguration": "readonly", "USBConnectionEvent": "readonly", "USBDevice": "readonly", "USBEndpoint": "readonly", "USBInterface": "readonly", "USBInTransferResult": "readonly", "USBIsochronousInTransferPacket": "readonly", "USBIsochronousInTransferResult": "readonly", "USBIsochronousOutTransferPacket": "readonly", "USBIsochronousOutTransferResult": "readonly", "USBOutTransferResult": "readonly", "UserActivation": "readonly", "ValidityState": "readonly", "VideoColorSpace": "readonly", "VideoDecoder": "readonly", "VideoEncoder": "readonly", "VideoFrame": "readonly", "VideoPlaybackQuality": "readonly", "ViewTimeline": "readonly", "ViewTransition": "readonly", "ViewTransitionTypeSet": "readonly", "VirtualKeyboard": "readonly", "VirtualKeyboardGeometryChangeEvent": "readonly", "VisibilityStateEntry": "readonly", "visualViewport": "readonly", "VisualViewport": "readonly", "VTTCue": "readonly", "VTTRegion": "readonly", "WakeLock": "readonly", "WakeLockSentinel": "readonly", "WaveShaperNode": "readonly", "WebGL2RenderingContext": "readonly", "WebGLActiveInfo": "readonly", "WebGLBuffer": "readonly", "WebGLContextEvent": "readonly", "WebGLFramebuffer": "readonly", "WebGLObject": "readonly", "WebGLProgram": "readonly", "WebGLQuery": "readonly", "WebGLRenderbuffer": "readonly", "WebGLRenderingContext": "readonly", "WebGLSampler": "readonly", "WebGLShader": "readonly", "WebGLShaderPrecisionFormat": "readonly", "WebGLSync": "readonly", "WebGLTexture": "readonly", "WebGLTransformFeedback": "readonly", "WebGLUniformLocation": "readonly", "WebGLVertexArrayObject": "readonly", "WebSocketError": "readonly", "WebSocketStream": "readonly", "WebTransport": "readonly", "WebTransportBidirectionalStream": "readonly", "WebTransportDatagramDuplexStream": "readonly", "WebTransportError": "readonly", "WebTransportReceiveStream": "readonly", "WebTransportSendStream": "readonly", "WGSLLanguageFeatures": "readonly", "WheelEvent": "readonly", "when": "readonly", "window": "readonly", "Window": "readonly", "WindowControlsOverlay": "readonly", "WindowControlsOverlayGeometryChangeEvent": "readonly", "Worker": "readonly", "Worklet": "readonly", "WorkletGlobalScope": "readonly", "XMLDocument": "readonly", "XMLHttpRequest": "readonly", "XMLHttpRequestEventTarget": "readonly", "XMLHttpRequestUpload": "readonly", "XMLSerializer": "readonly", "XPathEvaluator": "readonly", "XPathExpression": "readonly", "XPathResult": "readonly", "XRAnchor": "readonly", "XRAnchorSet": "readonly", "XRBoundedReferenceSpace": "readonly", "XRCamera": "readonly", "XRCPUDepthInformation": "readonly", "XRDepthInformation": "readonly", "XRDOMOverlayState": "readonly", "XRFrame": "readonly", "XRHand": "readonly", "XRHitTestResult": "readonly", "XRHitTestSource": "readonly", "XRInputSource": "readonly", "XRInputSourceArray": "readonly", "XRInputSourceEvent": "readonly", "XRInputSourcesChangeEvent": "readonly", "XRJointPose": "readonly", "XRJointSpace": "readonly", "XRLayer": "readonly", "XRLightEstimate": "readonly", "XRLightProbe": "readonly", "XRPose": "readonly", "XRRay": "readonly", "XRReferenceSpace": "readonly", "XRReferenceSpaceEvent": "readonly", "XRRenderState": "readonly", "XRRigidTransform": "readonly", "XRSession": "readonly", "XRSessionEvent": "readonly", "XRSpace": "readonly", "XRSystem": "readonly", "XRTransientInputHitTestResult": "readonly", "XRTransientInputHitTestSource": "readonly", "XRView": "readonly", "XRViewerPose": "readonly", "XRViewport": "readonly", "XRWebGLBinding": "readonly", "XRWebGLDepthInformation": "readonly", "XRWebGLLayer": "readonly", "XSLTProcessor": "readonly", "computed": "readonly", "defineEmits": "readonly", "defineExpose": "readonly", "defineProps": "readonly", "onMounted": "readonly", "onUnmounted": "readonly", "reactive": "readonly", "ref": "readonly", "shallowReactive": "readonly", "shallowRef": "readonly", "toRef": "readonly", "toRefs": "readonly", "watch": "readonly", "watchEffect": "readonly"}, "rules": {"array-callback-return": "error", "block-scoped-var": "error", "default-case-last": "error", "eqeqeq": ["error", "smart"], "new-cap": ["off", {"newIsCap": true, "capIsNew": false}], "no-alert": "error", "no-array-constructor": "error", "no-async-promise-executor": "error", "no-caller": "error", "no-case-declarations": "error", "no-class-assign": "error", "no-compare-neg-zero": "error", "no-cond-assign": ["error", "always"], "no-console": "off", "no-const-assign": "error", "no-control-regex": "error", "no-debugger": "error", "no-delete-var": "error", "no-dupe-class-members": "error", "no-dupe-keys": "error", "no-duplicate-case": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-empty-character-class": "off", "no-empty-pattern": "error", "no-eval": "error", "no-ex-assign": "error", "no-extend-native": "error", "no-extra-boolean-cast": "error", "no-fallthrough": "error", "no-func-assign": "error", "no-global-assign": "error", "no-import-assign": "error", "no-invalid-regexp": "off", "no-irregular-whitespace": "error", "no-iterator": "error", "no-labels": ["error", {"allowLoop": false, "allowSwitch": false}], "no-lone-blocks": "error", "no-loss-of-precision": "error", "no-multi-str": "error", "no-new": "error", "no-new-func": "error", "no-new-native-nonconstructor": "error", "no-new-wrappers": "error", "no-obj-calls": "error", "no-proto": "error", "no-prototype-builtins": "error", "no-redeclare": ["error", {"builtinGlobals": false}], "no-regex-spaces": "error", "no-restricted-globals": ["error", {"message": "Use `globalThis` instead.", "name": "global"}, {"message": "Use `globalThis` instead.", "name": "self"}], "no-self-assign": ["error", {"props": true}], "no-self-compare": "error", "no-shadow-restricted-names": "error", "no-sparse-arrays": "error", "no-template-curly-in-string": "error", "no-this-before-super": "error", "no-throw-literal": "error", "no-unexpected-multiline": "error", "no-unneeded-ternary": ["error", {"defaultAssignment": false}], "no-unsafe-finally": "error", "no-unsafe-negation": "error", "no-unused-expressions": ["error", {"allowShortCircuit": true, "allowTaggedTemplates": true, "allowTernary": true}], "no-unused-vars": ["error", {"args": "none", "caughtErrors": "none", "ignoreRestSiblings": true, "vars": "all"}], "no-useless-backreference": "off", "no-useless-call": "error", "no-useless-catch": "error", "no-useless-constructor": "error", "no-useless-rename": "error", "no-var": "error", "no-with": "error", "prefer-exponentiation-operator": "error", "prefer-promise-reject-errors": "error", "prefer-rest-params": "error", "prefer-spread": "error", "symbol-description": "error", "unicode-bom": ["error", "never"], "use-isnan": ["error", {"enforceForIndexOf": true, "enforceForSwitchCase": true}], "valid-typeof": ["error", {"requireStringLiterals": true}], "vars-on-top": "error", "yoda": ["error", "never"], "node/no-exports-assign": "error", "node/no-new-require": "error", "jsdoc/check-access": "warn", "jsdoc/check-property-names": "warn", "jsdoc/empty-tags": "warn", "jsdoc/implements-on-classes": "warn", "jsdoc/no-defaults": "warn", "jsdoc/require-param-name": "warn", "jsdoc/require-property": "warn", "jsdoc/require-property-description": "warn", "jsdoc/require-property-name": "warn", "jsdoc/require-returns-description": "warn", "import/consistent-type-specifier-style": ["error", "prefer-top-level"], "import/first": "error", "import/no-duplicates": "error", "import/no-mutable-exports": "error", "import/no-named-default": "error", "import/no-self-import": "error", "import/no-webpack-loader-syntax": "error", "unicorn/consistent-empty-array-spread": "error", "unicorn/error-message": "error", "unicorn/escape-case": "error", "unicorn/new-for-builtins": "error", "unicorn/no-new-array": "error", "unicorn/no-new-buffer": "error", "unicorn/number-literal-case": "error", "unicorn/prefer-dom-node-text-content": "error", "unicorn/prefer-includes": "error", "unicorn/prefer-node-protocol": "error", "unicorn/prefer-number-properties": "error", "unicorn/prefer-string-starts-ends-with": "error", "unicorn/prefer-type-error": "error", "unicorn/throw-new-error": "error"}, "overrides": [{"files": ["**/*.?([cm])ts", "**/*.?([cm])tsx", "**/*.vue"], "rules": {"no-class-assign": "off", "no-const-assign": "off", "no-dupe-class-members": "off", "no-dupe-keys": "off", "no-func-assign": "off", "no-import-assign": "off", "no-new-native-nonconstructor": "off", "no-obj-calls": "off", "no-redeclare": "off", "no-setter-return": "off", "no-this-before-super": "off", "no-unsafe-negation": "off", "no-with": "off", "no-array-constructor": "off", "no-unused-expressions": "off", "no-unused-vars": "off", "no-useless-constructor": "off"}}, {"files": ["**/__tests__/**/*.?([cm])[jt]s?(x)", "**/*.spec.?([cm])[jt]s?(x)", "**/*.test.?([cm])[jt]s?(x)", "**/*.bench.?([cm])[jt]s?(x)", "**/*.benchmark.?([cm])[jt]s?(x)"], "rules": {"no-unused-expressions": "off"}}, {"files": ["**/*.md/**/*.?([cm])[jt]s?(x)", "**/*.md/**/*.vue"], "rules": {"no-alert": "off", "no-labels": "off", "no-lone-blocks": "off", "no-unused-expressions": "off", "no-unused-labels": "off", "no-unused-vars": "off", "unicode-bom": "off"}}, {"files": ["**/*.d.?([cm])ts"], "rules": {"import/no-duplicates": "off"}}]}