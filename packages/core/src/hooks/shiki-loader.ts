// 支持的语言映射表（你可以根据实际支持的语言增删）
export const languageLoaders: Record<string, () => Promise<any>> = {
  abap: () => import('shiki/dist/langs/abap.mjs'),
  'actionscript-3': () => import('shiki/dist/langs/actionscript-3.mjs'),
  ada: () => import('shiki/dist/langs/ada.mjs'),
  apache: () => import('shiki/dist/langs/apache.mjs'),
  apex: () => import('shiki/dist/langs/apex.mjs'),
  apl: () => import('shiki/dist/langs/apl.mjs'),
  applescript: () => import('shiki/dist/langs/applescript.mjs'),
  ara: () => import('shiki/dist/langs/ara.mjs'),
  asm: () => import('shiki/dist/langs/asm.mjs'),
  astro: () => import('shiki/dist/langs/astro.mjs'),
  awk: () => import('shiki/dist/langs/awk.mjs'),
  ballerina: () => import('shiki/dist/langs/ballerina.mjs'),
  bash: () => import('shiki/dist/langs/bash.mjs'),
  bat: () => import('shiki/dist/langs/bat.mjs'),
  batch: () => import('shiki/dist/langs/batch.mjs'),
  be: () => import('shiki/dist/langs/be.mjs'),
  beancount: () => import('shiki/dist/langs/beancount.mjs'),
  berry: () => import('shiki/dist/langs/berry.mjs'),
  bibtex: () => import('shiki/dist/langs/bibtex.mjs'),
  bicep: () => import('shiki/dist/langs/bicep.mjs'),
  blade: () => import('shiki/dist/langs/blade.mjs'),
  c: () => import('shiki/dist/langs/c.mjs'),
  'c#': () => import('shiki/dist/langs/csharp.mjs'),
  cadence: () => import('shiki/dist/langs/cadence.mjs'),
  cdc: () => import('shiki/dist/langs/cdc.mjs'),
  clarity: () => import('shiki/dist/langs/clarity.mjs'),
  clj: () => import('shiki/dist/langs/clj.mjs'),
  clojure: () => import('shiki/dist/langs/clojure.mjs'),
  cmake: () => import('shiki/dist/langs/cmake.mjs'),
  cmd: () => import('shiki/dist/langs/cmd.mjs'),
  cobol: () => import('shiki/dist/langs/cobol.mjs'),
  codeql: () => import('shiki/dist/langs/codeql.mjs'),
  coffee: () => import('shiki/dist/langs/coffee.mjs'),
  console: () => import('shiki/dist/langs/console.mjs'),
  cpp: () => import('shiki/dist/langs/cpp.mjs'),
  cql: () => import('shiki/dist/langs/cql.mjs'),
  crystal: () => import('shiki/dist/langs/crystal.mjs'),
  cs: () => import('shiki/dist/langs/cs.mjs'),
  csharp: () => import('shiki/dist/langs/csharp.mjs'),
  css: () => import('shiki/dist/langs/css.mjs'),
  cue: () => import('shiki/dist/langs/cue.mjs'),
  cypher: () => import('shiki/dist/langs/cypher.mjs'),
  d: () => import('shiki/dist/langs/d.mjs'),
  dart: () => import('shiki/dist/langs/dart.mjs'),
  dax: () => import('shiki/dist/langs/dax.mjs'),
  diff: () => import('shiki/dist/langs/diff.mjs'),
  docker: () => import('shiki/dist/langs/docker.mjs'),
  dockerfile: () => import('shiki/dist/langs/dockerfile.mjs'),
  'dream-maker': () => import('shiki/dist/langs/dream-maker.mjs'),
  elixir: () => import('shiki/dist/langs/elixir.mjs'),
  elm: () => import('shiki/dist/langs/elm.mjs'),
  erb: () => import('shiki/dist/langs/erb.mjs'),
  erl: () => import('shiki/dist/langs/erl.mjs'),
  erlang: () => import('shiki/dist/langs/erlang.mjs'),
  'f#': () => import('shiki/dist/langs/fsharp.mjs'),
  fish: () => import('shiki/dist/langs/fish.mjs'),
  fs: () => import('shiki/dist/langs/fs.mjs'),
  fsharp: () => import('shiki/dist/langs/fsharp.mjs'),
  fsl: () => import('shiki/dist/langs/fsl.mjs'),
  gdresource: () => import('shiki/dist/langs/gdresource.mjs'),
  gdscript: () => import('shiki/dist/langs/gdscript.mjs'),
  gdshader: () => import('shiki/dist/langs/gdshader.mjs'),
  gherkin: () => import('shiki/dist/langs/gherkin.mjs'),
  'git-commit': () => import('shiki/dist/langs/git-commit.mjs'),
  'git-rebase': () => import('shiki/dist/langs/git-rebase.mjs'),
  gjs: () => import('shiki/dist/langs/gjs.mjs'),
  'glimmer-js': () => import('shiki/dist/langs/glimmer-js.mjs'),
  'glimmer-ts': () => import('shiki/dist/langs/glimmer-ts.mjs'),
  glsl: () => import('shiki/dist/langs/glsl.mjs'),
  gnuplot: () => import('shiki/dist/langs/gnuplot.mjs'),
  go: () => import('shiki/dist/langs/go.mjs'),
  graphql: () => import('shiki/dist/langs/graphql.mjs'),
  groovy: () => import('shiki/dist/langs/groovy.mjs'),
  gts: () => import('shiki/dist/langs/gts.mjs'),
  hack: () => import('shiki/dist/langs/hack.mjs'),
  haml: () => import('shiki/dist/langs/haml.mjs'),
  handlebars: () => import('shiki/dist/langs/handlebars.mjs'),
  haskell: () => import('shiki/dist/langs/haskell.mjs'),
  hbs: () => import('shiki/dist/langs/hbs.mjs'),
  hcl: () => import('shiki/dist/langs/hcl.mjs'),
  hjson: () => import('shiki/dist/langs/hjson.mjs'),
  hlsl: () => import('shiki/dist/langs/hlsl.mjs'),
  hs: () => import('shiki/dist/langs/hs.mjs'),
  html: () => import('shiki/dist/langs/html.mjs'),
  http: () => import('shiki/dist/langs/http.mjs'),
  imba: () => import('shiki/dist/langs/imba.mjs'),
  ini: () => import('shiki/dist/langs/ini.mjs'),
  jade: () => import('shiki/dist/langs/jade.mjs'),
  java: () => import('shiki/dist/langs/java.mjs'),
  javascript: () => import('shiki/dist/langs/javascript.mjs'),
  'jinja-html': () => import('shiki/dist/langs/jinja-html.mjs'),
  jison: () => import('shiki/dist/langs/jison.mjs'),
  js: () => import('shiki/dist/langs/js.mjs'),
  json: () => import('shiki/dist/langs/json.mjs'),
  json5: () => import('shiki/dist/langs/json5.mjs'),
  jsonc: () => import('shiki/dist/langs/jsonc.mjs'),
  jsonl: () => import('shiki/dist/langs/jsonl.mjs'),
  jsonnet: () => import('shiki/dist/langs/jsonnet.mjs'),
  jssm: () => import('shiki/dist/langs/jssm.mjs'),
  jsx: () => import('shiki/dist/langs/jsx.mjs'),
  julia: () => import('shiki/dist/langs/julia.mjs'),
  kotlin: () => import('shiki/dist/langs/kotlin.mjs'),
  kql: () => import('shiki/dist/langs/kql.mjs'),
  kusto: () => import('shiki/dist/langs/kusto.mjs'),
  latex: () => import('shiki/dist/langs/latex.mjs'),
  less: () => import('shiki/dist/langs/less.mjs'),
  liquid: () => import('shiki/dist/langs/liquid.mjs'),
  lisp: () => import('shiki/dist/langs/lisp.mjs'),
  logo: () => import('shiki/dist/langs/logo.mjs'),
  lua: () => import('shiki/dist/langs/lua.mjs'),
  make: () => import('shiki/dist/langs/make.mjs'),
  makefile: () => import('shiki/dist/langs/makefile.mjs'),
  markdown: () => import('shiki/dist/langs/markdown.mjs'),
  marko: () => import('shiki/dist/langs/marko.mjs'),
  matlab: () => import('shiki/dist/langs/matlab.mjs'),
  md: () => import('shiki/dist/langs/md.mjs'),
  mdc: () => import('shiki/dist/langs/mdc.mjs'),
  mdx: () => import('shiki/dist/langs/mdx.mjs'),
  mermaid: () => import('shiki/dist/langs/mermaid.mjs'),
  mojo: () => import('shiki/dist/langs/mojo.mjs'),
  nar: () => import('shiki/dist/langs/nar.mjs'),
  narrat: () => import('shiki/dist/langs/narrat.mjs'),
  nextflow: () => import('shiki/dist/langs/nextflow.mjs'),
  nf: () => import('shiki/dist/langs/nf.mjs'),
  nginx: () => import('shiki/dist/langs/nginx.mjs'),
  nim: () => import('shiki/dist/langs/nim.mjs'),
  nix: () => import('shiki/dist/langs/nix.mjs'),
  objc: () => import('shiki/dist/langs/objc.mjs'),
  'objective-c': () => import('shiki/dist/langs/objective-c.mjs'),
  'objective-cpp': () => import('shiki/dist/langs/objective-cpp.mjs'),
  ocaml: () => import('shiki/dist/langs/ocaml.mjs'),
  pascal: () => import('shiki/dist/langs/pascal.mjs'),
  perl: () => import('shiki/dist/langs/perl.mjs'),
  perl6: () => import('shiki/dist/langs/perl6.mjs'),
  php: () => import('shiki/dist/langs/php.mjs'),
  plsql: () => import('shiki/dist/langs/plsql.mjs'),
  postcss: () => import('shiki/dist/langs/postcss.mjs'),
  powerquery: () => import('shiki/dist/langs/powerquery.mjs'),
  powershell: () => import('shiki/dist/langs/powershell.mjs'),
  prisma: () => import('shiki/dist/langs/prisma.mjs'),
  prolog: () => import('shiki/dist/langs/prolog.mjs'),
  properties: () => import('shiki/dist/langs/properties.mjs'),
  proto: () => import('shiki/dist/langs/proto.mjs'),
  ps: () => import('shiki/dist/langs/ps.mjs'),
  ps1: () => import('shiki/dist/langs/ps1.mjs'),
  pug: () => import('shiki/dist/langs/pug.mjs'),
  puppet: () => import('shiki/dist/langs/puppet.mjs'),
  purescript: () => import('shiki/dist/langs/purescript.mjs'),
  py: () => import('shiki/dist/langs/py.mjs'),
  python: () => import('shiki/dist/langs/python.mjs'),
  ql: () => import('shiki/dist/langs/ql.mjs'),
  r: () => import('shiki/dist/langs/r.mjs'),
  raku: () => import('shiki/dist/langs/raku.mjs'),
  razor: () => import('shiki/dist/langs/razor.mjs'),
  rb: () => import('shiki/dist/langs/rb.mjs'),
  reg: () => import('shiki/dist/langs/reg.mjs'),
  rel: () => import('shiki/dist/langs/rel.mjs'),
  riscv: () => import('shiki/dist/langs/riscv.mjs'),
  rs: () => import('shiki/dist/langs/rs.mjs'),
  rst: () => import('shiki/dist/langs/rst.mjs'),
  ruby: () => import('shiki/dist/langs/ruby.mjs'),
  rust: () => import('shiki/dist/langs/rust.mjs'),
  sas: () => import('shiki/dist/langs/sas.mjs'),
  sass: () => import('shiki/dist/langs/sass.mjs'),
  scala: () => import('shiki/dist/langs/scala.mjs'),
  scheme: () => import('shiki/dist/langs/scheme.mjs'),
  scss: () => import('shiki/dist/langs/scss.mjs'),
  sh: () => import('shiki/dist/langs/sh.mjs'),
  shader: () => import('shiki/dist/langs/shader.mjs'),
  shaderlab: () => import('shiki/dist/langs/shaderlab.mjs'),
  shell: () => import('shiki/dist/langs/shell.mjs'),
  shellscript: () => import('shiki/dist/langs/shellscript.mjs'),
  shellsession: () => import('shiki/dist/langs/shellsession.mjs'),
  smalltalk: () => import('shiki/dist/langs/smalltalk.mjs'),
  solidity: () => import('shiki/dist/langs/solidity.mjs'),
  sparql: () => import('shiki/dist/langs/sparql.mjs'),
  spl: () => import('shiki/dist/langs/spl.mjs'),
  splunk: () => import('shiki/dist/langs/splunk.mjs'),
  sql: () => import('shiki/dist/langs/sql.mjs'),
  'ssh-config': () => import('shiki/dist/langs/ssh-config.mjs'),
  stata: () => import('shiki/dist/langs/stata.mjs'),
  styl: () => import('shiki/dist/langs/styl.mjs'),
  stylus: () => import('shiki/dist/langs/stylus.mjs'),
  svelte: () => import('shiki/dist/langs/svelte.mjs'),
  swift: () => import('shiki/dist/langs/swift.mjs'),
  'system-verilog': () => import('shiki/dist/langs/system-verilog.mjs'),
  tasl: () => import('shiki/dist/langs/tasl.mjs'),
  tcl: () => import('shiki/dist/langs/tcl.mjs'),
  tex: () => import('shiki/dist/langs/tex.mjs'),
  toml: () => import('shiki/dist/langs/toml.mjs'),
  ts: () => import('shiki/dist/langs/ts.mjs'),
  tsx: () => import('shiki/dist/langs/tsx.mjs'),
  turtle: () => import('shiki/dist/langs/turtle.mjs'),
  twig: () => import('shiki/dist/langs/twig.mjs'),
  typescript: () => import('shiki/dist/langs/typescript.mjs'),
  v: () => import('shiki/dist/langs/v.mjs'),
  vb: () => import('shiki/dist/langs/vb.mjs'),
  verilog: () => import('shiki/dist/langs/verilog.mjs'),
  vhdl: () => import('shiki/dist/langs/vhdl.mjs'),
  vim: () => import('shiki/dist/langs/vim.mjs'),
  viml: () => import('shiki/dist/langs/viml.mjs'),
  vimscript: () => import('shiki/dist/langs/vimscript.mjs'),
  vue: () => import('shiki/dist/langs/vue.mjs'),
  'vue-html': () => import('shiki/dist/langs/vue-html.mjs'),
  vy: () => import('shiki/dist/langs/vy.mjs'),
  vyper: () => import('shiki/dist/langs/vyper.mjs'),
  wasm: () => import('shiki/dist/langs/wasm.mjs'),
  wenyan: () => import('shiki/dist/langs/wenyan.mjs'),
  wgsl: () => import('shiki/dist/langs/wgsl.mjs'),
  wolfram: () => import('shiki/dist/langs/wolfram.mjs'),
  xml: () => import('shiki/dist/langs/xml.mjs'),
  xsl: () => import('shiki/dist/langs/xsl.mjs'),
  yaml: () => import('shiki/dist/langs/yaml.mjs'),
  yml: () => import('shiki/dist/langs/yml.mjs'),
  zenscript: () => import('shiki/dist/langs/zenscript.mjs'),
  zig: () => import('shiki/dist/langs/zig.mjs'),
  zsh: () => import('shiki/dist/langs/zsh.mjs'),
  文言: () => import('shiki/dist/langs/wenyan.mjs')
};

export const themeLoaders: Record<string, () => Promise<any>> = {
  andromeeda: () => import('shiki/dist/themes/andromeeda.mjs'),
  'aurora-x': () => import('shiki/dist/themes/aurora-x.mjs'),
  'ayu-dark': () => import('shiki/dist/themes/ayu-dark.mjs'),
  'catppuccin-frappe': () => import('shiki/dist/themes/catppuccin-frappe.mjs'),
  'catppuccin-latte': () => import('shiki/dist/themes/catppuccin-latte.mjs'),
  'catppuccin-macchiato': () =>
    import('shiki/dist/themes/catppuccin-macchiato.mjs'),
  'catppuccin-mocha': () => import('shiki/dist/themes/catppuccin-mocha.mjs'),
  'dark-plus': () => import('shiki/dist/themes/dark-plus.mjs'),
  dracula: () => import('shiki/dist/themes/dracula.mjs'),
  'dracula-soft': () => import('shiki/dist/themes/dracula-soft.mjs'),
  'everforest-dark': () => import('shiki/dist/themes/everforest-dark.mjs'),
  'everforest-light': () => import('shiki/dist/themes/everforest-light.mjs'),
  'github-dark': () => import('shiki/dist/themes/github-dark.mjs'),
  'github-dark-default': () =>
    import('shiki/dist/themes/github-dark-default.mjs'),
  'github-dark-dimmed': () =>
    import('shiki/dist/themes/github-dark-dimmed.mjs'),
  'github-dark-high-contrast': () =>
    import('shiki/dist/themes/github-dark-high-contrast.mjs'),
  'github-light': () => import('shiki/dist/themes/github-light.mjs'),
  'github-light-default': () =>
    import('shiki/dist/themes/github-light-default.mjs'),
  'github-light-high-contrast': () =>
    import('shiki/dist/themes/github-light-high-contrast.mjs'),
  'gruvbox-dark-hard': () => import('shiki/dist/themes/gruvbox-dark-hard.mjs'),
  'gruvbox-dark-medium': () =>
    import('shiki/dist/themes/gruvbox-dark-medium.mjs'),
  'gruvbox-dark-soft': () => import('shiki/dist/themes/gruvbox-dark-soft.mjs'),
  'gruvbox-light-hard': () =>
    import('shiki/dist/themes/gruvbox-light-hard.mjs'),
  'gruvbox-light-medium': () =>
    import('shiki/dist/themes/gruvbox-light-medium.mjs'),
  'gruvbox-light-soft': () =>
    import('shiki/dist/themes/gruvbox-light-soft.mjs'),
  houston: () => import('shiki/dist/themes/houston.mjs'),
  'kanagawa-dragon': () => import('shiki/dist/themes/kanagawa-dragon.mjs'),
  'kanagawa-lotus': () => import('shiki/dist/themes/kanagawa-lotus.mjs'),
  'kanagawa-wave': () => import('shiki/dist/themes/kanagawa-wave.mjs'),
  laserwave: () => import('shiki/dist/themes/laserwave.mjs'),
  'light-plus': () => import('shiki/dist/themes/light-plus.mjs'),
  'material-theme': () => import('shiki/dist/themes/material-theme.mjs'),
  'material-theme-darker': () =>
    import('shiki/dist/themes/material-theme-darker.mjs'),
  'material-theme-lighter': () =>
    import('shiki/dist/themes/material-theme-lighter.mjs'),
  'material-theme-ocean': () =>
    import('shiki/dist/themes/material-theme-ocean.mjs'),
  'material-theme-palenight': () =>
    import('shiki/dist/themes/material-theme-palenight.mjs'),
  'min-dark': () => import('shiki/dist/themes/min-dark.mjs'),
  'min-light': () => import('shiki/dist/themes/min-light.mjs'),
  monokai: () => import('shiki/dist/themes/monokai.mjs'),
  'night-owl': () => import('shiki/dist/themes/night-owl.mjs'),
  nord: () => import('shiki/dist/themes/nord.mjs'),
  'one-dark-pro': () => import('shiki/dist/themes/one-dark-pro.mjs'),
  'one-light': () => import('shiki/dist/themes/one-light.mjs'),
  plastic: () => import('shiki/dist/themes/plastic.mjs'),
  poimandres: () => import('shiki/dist/themes/poimandres.mjs'),
  red: () => import('shiki/dist/themes/red.mjs'),
  'rose-pine': () => import('shiki/dist/themes/rose-pine.mjs'),
  'rose-pine-dawn': () => import('shiki/dist/themes/rose-pine-dawn.mjs'),
  'rose-pine-moon': () => import('shiki/dist/themes/rose-pine-moon.mjs'),
  'slack-dark': () => import('shiki/dist/themes/slack-dark.mjs'),
  'slack-ochin': () => import('shiki/dist/themes/slack-ochin.mjs'),
  'snazzy-light': () => import('shiki/dist/themes/snazzy-light.mjs'),
  'solarized-dark': () => import('shiki/dist/themes/solarized-dark.mjs'),
  'solarized-light': () => import('shiki/dist/themes/solarized-light.mjs'),
  'synthwave-84': () => import('shiki/dist/themes/synthwave-84.mjs'),
  'tokyo-night': () => import('shiki/dist/themes/tokyo-night.mjs'),
  vesper: () => import('shiki/dist/themes/vesper.mjs'),
  'vitesse-black': () => import('shiki/dist/themes/vitesse-black.mjs'),
  'vitesse-dark': () => import('shiki/dist/themes/vitesse-dark.mjs'),
  'vitesse-light': () => import('shiki/dist/themes/vitesse-light.mjs')
};
