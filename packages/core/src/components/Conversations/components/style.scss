.conversation-item {
  padding: 14px 10px;
  margin-right: 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  & + & {
    margin-top: 4px;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: #c0c4cc;
  }

  &.active {
    background-color: #f0f0f0;
  }

  &.hovered,
  &:hover {
    background-color: #f0f0f0;
  }

  &.menu-opened {
    background-color: #f0f0f0;
  }
}

.conversation-content {
  display: flex;
  align-items: center;
  height: var(--conversation-label-height, 20px);

  .conversation-prefix-icon {
    margin-right: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .conversation-content-main {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  .conversation-label-container {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
  }

  .conversation-label {
    font-size: 14px;
    color: #303133;
    position: relative;
    white-space: nowrap;

    &.text-gradient {
      mask-image: linear-gradient(to right, black 60%, transparent 100%);
      -webkit-mask-image: linear-gradient(to right, black 60%, transparent 100%);
    }
  }

  .conversation-timestamp {
    font-size: 14px;
    color: #909399;
    margin-left: 8px;
  }

  .conversation-suffix-icon {
    margin-left: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .conversation-dropdown-more {
    justify-self: center;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .conversation-dropdown-more-icon {
    font-size: 16px;
    padding: 2px;
    border-radius: 5px;
    &:hover {
      background-color: #d3d3d3;
    }
  }

  .conversation-menu {
    margin-left: 8px;
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.2s ease;

    .hovered &,
    .active & {
      opacity: 1;
    }
  }
}
