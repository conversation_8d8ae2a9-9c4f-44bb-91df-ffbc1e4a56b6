import type {
  BundledLanguage,
  BundledTheme,
  LanguageInput,
  StringLiteralUnion,
  ThemeRegistrationAny
} from 'shiki';

// 初始化Shiki 高亮器配置
export interface InitShikiOptions {
  // 语言列表
  langs: Array<LanguageInput | BundledLanguage> | undefined;
  // 主题列表
  themes: Partial<
    Record<
      string | 'light' | 'dark',
      ThemeRegistrationAny | StringLiteralUnion<BundledTheme, string>
    >
  >;
  /**
   * 自定义当前主题下的代码颜色配置
   *
   * 一个颜色名称到新颜色值的映射表。
   *
   * 注意: 颜色的键必须以 `#` 开头，并且应为小写格式 ,否则不生效。
   *
   * 如果主题本身也定义了 `colorReplacements`，这个映射会与其合并。
   *
   * 最好和当前主题对应着修改
   *
   * @template
   * ```typescript
   * {
   *  "vitesse-light": {
   *    "#ab5959": "#ff66ff"
   *  },
   *  "vitesse-dark": {
   *    "#cb7676": "#ff0066"
   *  }
   * }
   * ```
   */
  colorReplacements: Record<string, string | Record<string, string>>;
}

export const shikiThemeDefault: InitShikiOptions['themes'] = {
  light: 'vitesse-light',
  dark: 'vitesse-dark'
};

export const SHIKI_SUPPORT_LANGS = [
  'abap',
  'actionscript-3',
  'ada',
  'apache',
  'apex',
  'apl',
  'applescript',
  'ara',
  'asm',
  'astro',
  'awk',
  'ballerina',
  'bat',
  'beancount',
  'berry',
  'bibtex',
  'bicep',
  'blade',
  'c',
  'cadence',
  'clarity',
  'clojure',
  'cmake',
  'cobol',
  'codeql',
  'coffee',
  'cpp',
  'crystal',
  'csharp',
  'css',
  'cue',
  'cypher',
  'd',
  'dart',
  'dax',
  'diff',
  'docker',
  'dream-maker',
  'elixir',
  'elm',
  'erb',
  'erlang',
  'fish',
  'fsharp',
  'gdresource',
  'gdscript',
  'gdshader',
  'gherkin',
  'git-commit',
  'git-rebase',
  'glimmer-js',
  'glimmer-ts',
  'glsl',
  'gnuplot',
  'go',
  'graphql',
  'groovy',
  'hack',
  'haml',
  'handlebars',
  'haskell',
  'hcl',
  'hjson',
  'hlsl',
  'html',
  'http',
  'imba',
  'ini',
  'java',
  'javascript',
  'jinja-html',
  'jison',
  'json',
  'json5',
  'jsonc',
  'jsonl',
  'jsonnet',
  'jssm',
  'jsx',
  'julia',
  'kotlin',
  'kusto',
  'latex',
  'less',
  'liquid',
  'lisp',
  'logo',
  'lua',
  'make',
  'markdown',
  'marko',
  'matlab',
  'mdc',
  'mdx',
  'mermaid',
  'mojo',
  'narrat',
  'nextflow',
  'nginx',
  'nim',
  'nix',
  'objective-c',
  'objective-cpp',
  'ocaml',
  'pascal',
  'perl',
  'php',
  'plsql',
  'postcss',
  'powerquery',
  'powershell',
  'prisma',
  'prolog',
  'proto',
  'pug',
  'puppet',
  'purescript',
  'python',
  'r',
  'raku',
  'razor',
  'reg',
  'rel',
  'riscv',
  'rst',
  'ruby',
  'rust',
  'sas',
  'sass',
  'scala',
  'scheme',
  'scss',
  'shaderlab',
  'shellscript',
  'shellsession',
  'smalltalk',
  'solidity',
  'sparql',
  'splunk',
  'sql',
  'ssh-config',
  'stata',
  'stylus',
  'svelte',
  'swift',
  'system-verilog',
  'tasl',
  'tcl',
  'tex',
  'toml',
  'tsx',
  'turtle',
  'twig',
  'typescript',
  'v',
  'vb',
  'verilog',
  'vhdl',
  'viml',
  'vue',
  'vue-html',
  'vyper',
  'wasm',
  'wenyan',
  'wgsl',
  'wolfram',
  'xml',
  'xsl',
  'yaml',
  'zenscript',
  'zig',
  'bash',
  'batch',
  'be',
  'c#',
  'cdc',
  'clj',
  'cmd',
  'console',
  'cql',
  'cs',
  'dockerfile',
  'erl',
  'f#',
  'fs',
  'fsl',
  'gjs',
  'gts',
  'hbs',
  'hs',
  'jade',
  'js',
  'kql',
  'makefile',
  'md',
  'nar',
  'nf',
  'objc',
  'perl6',
  'properties',
  'ps',
  'ps1',
  'py',
  'ql',
  'rb',
  'rs',
  'sh',
  'shader',
  'shell',
  'spl',
  'styl',
  'ts',
  'vim',
  'vimscript',
  'vy',
  'yml',
  'zsh',
  '文言'
] as const;
