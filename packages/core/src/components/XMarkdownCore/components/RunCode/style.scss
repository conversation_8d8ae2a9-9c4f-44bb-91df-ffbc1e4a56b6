.elx-run-code-dialog,
.elx-run-code-drawer {
  width: 75% !important;
  background-color: var(--shiki-code-header-bg) !important;
  .el-dialog__body {
    overflow: auto;
    border-radius: var(--shiki-custom-brr);
  }
  .el-drawer__body {
    overflow: auto;
    border-radius: var(--shiki-custom-brr);
  }
  .el-drawer__header {
    position: relative;
    margin-bottom: 0 !important;
  }
  .el-dialog__headerbtn,
  .el-drawer__close-btn {
    color: var(--shiki-code-header-span-color);
    width: 32px;
    height: 32px;
    padding: 5px;
    box-sizing: border-box;
    right: 10px;
    top: 15px;
    font-size: 20px;
    border-radius: var(--shiki-custom-brr);
    &:hover {
      background-color: var(--shiki-code-header-btn-bg);
    }
  }

  .view-code-close-btn {
    background-color: transparent;
    border: none;
    span {
      color: var(--shiki-code-header-span-color);
      width: 32px;
      height: 32px;
      &:hover {
        color: var(--el-color-primary);
      }
    }
    position: absolute;
    width: 32px;
    height: 32px;
    padding: 5px;
    box-sizing: border-box;
    right: 10px;
    top: 15px;
    font-size: 20px;
    border-radius: var(--shiki-custom-brr);
    &:hover {
      background-color: var(--shiki-code-header-btn-bg);
    }
  }

  .customCloseBtn {
    &:hover {
      background-color: transparent !important;
    }
  }
}

// 媒体查询

@media (max-width: 768px) {
  .elx-run-code-dialog,
  .elx-run-code-drawer {
    width: 100% !important;
  }
}

.elx-run-code-content-view-iframe {
  height: 713px;
  overflow: hidden;
}

.elx-run-code-dialog-view {
  .el-dialog__body {
    border: 1px solid transparent !important;
  }
  .el-drawer__body {
    border: 1px solid transparent !important;
  }
}
