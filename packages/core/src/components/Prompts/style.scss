.el-prompts {
  display: flex;
  flex-direction: column;
  .el-prompts-title {
    margin-block-start: 0;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 0.5em;
    font-size: 16px;
    line-height: 1.5;
    box-sizing: border-box;
    word-break: break-word;
    font-family:
      BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
      'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }

  .el-prompts-items {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scrollbar-width: none;
    list-style: none;
    padding-inline-start: 0;
    margin-block: 0;
    align-items: stretch;
    box-sizing: border-box;
  }

  .el-prompts-items-wrap {
    flex-wrap: wrap;
  }

  .el-prompts-items-vertical {
    flex-direction: column;
    align-items: flex-start;
  }

  .el-prompts-item {
    flex: none;
    display: flex;
    height: 100%;
    padding-block: 12px;
    padding-inline: 16px;
    align-items: flex-start;
    justify-content: flex-start;
    background: #ffffff;
    border-radius: 8px;
    transition:
      border 0.3s,
      background 0.3s;
    border: 1px solid #f0f0f0;
  }

  .el-prompts-item-disabled {
    pointer-events: none;
    background: rgba(0, 0, 0, 0.04);
  }

  .el-prompts-item-gap {
    gap: 8px;
  }

  .hovered {
    cursor: pointer;
    background: rgba(0, 0, 0, 0.08);
  }

  .actived {
    background: rgba(0, 0, 0, 0.15);
  }

  .item-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .el-prompts-item-content {
    flex: auto;
    min-width: 0;
    display: flex;
    gap: 4px;
    flex-direction: column;
    align-items: flex-start;
    .el-prompts-item-icon {
      margin: 0;
      padding: 0;
      font-size: 14px;
      line-height: 1.5;
      text-align: start;
      white-space: normal;
    }

    .el-prompts-item-label {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 500;
      margin: 0;
      padding: 0;
      font-size: 14px;
      line-height: 1.5;
      text-align: start;
      white-space: normal;
    }
    .el-prompts-item-description {
      color: rgba(0, 0, 0, 0.45);
      margin: 0;
      padding: 0;
      font-size: 14px;
      line-height: 1.5;
      text-align: start;
      white-space: normal;
    }
  }

  // 限制子项的宽度
  .el-prompts-children {
    .el-prompts-items-vertical {
      align-items: initial;
    }
  }
}
