{
  // 开启扁平化配置
  "eslint.useFlatConfig": true,

  // 关闭默认的配置，我们这里默认不开启prettier格式化
  "prettier.enable": false,
  // 关闭默认格式化
  "editor.formatOnSave": false,

  // 保存自动修复
  "editor.codeActionsOnSave": {
    // 我们这里是指定自定义的修复
    "source.fixAll.eslint": "explicit",
    // 来源导入我们不需要给关闭掉
    "source.organizeImports": "never",
    // 使用 stylelint 来修复样式问题
    "source.fixAll.stylelint": "explicit",
    "source.fixAll.oxc": "explicit"
  },

  // 在eslin中开启哪些语言的校验
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],
  "oxc.enable": true,
  "cSpell.words": [
    "abap",
    "actionscript",
    "aeac",
    "andromeeda",
    "applescript",
    "beancount",
    "bibtex",
    "borderless",
    "catppuccin",
    "chatarea",
    "deepmerge",
    "esno",
    "everforest",
    "gdresource",
    "gdscript",
    "gdshader",
    "glsl",
    "Groupable",
    "gruvbox",
    "haml",
    "headerbtn",
    "hjson",
    "hlsl",
    "imba",
    "jison",
    "jsonlee",
    "jsonnet",
    "jssm",
    "kanagawa",
    "katex",
    "kusto",
    "laserwave",
    "macchiato",
    "marko",
    "mdast",
    "monokai",
    "narrat",
    "nextflow",
    "noopener",
    "nord",
    "noreferrer",
    "objc",
    "ochin",
    "palenight",
    "poimandres",
    "powerquery",
    "purescript",
    "radash",
    "raku",
    "rehype",
    "riscv",
    "ruoyi",
    "shellsession",
    "shiki",
    "shikijs",
    "sparql",
    "stata",
    "styl",
    "swrv",
    "synthwave",
    "tasl",
    "treemap",
    "unplugin",
    "verilog",
    "vhdl",
    "viml",
    "vimscript",
    "vitesse",
    "Vnode",
    "vueuse",
    "wenyan",
    "wgsl",
    "xmarkdown",
    "zenscript"
  ],
  "eslint.nodePath": "node_modules",

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off", "fixable": true },
    { "rule": "format/*", "severity": "off", "fixable": true },
    { "rule": "*-indent", "severity": "off", "fixable": true },
    { "rule": "*-spacing", "severity": "off", "fixable": true },
    { "rule": "*-spaces", "severity": "off", "fixable": true },
    { "rule": "*-order", "severity": "off", "fixable": true },
    { "rule": "*-dangle", "severity": "off", "fixable": true },
    { "rule": "*-newline", "severity": "off", "fixable": true },
    { "rule": "*quotes", "severity": "off", "fixable": true },
    { "rule": "*semi", "severity": "off", "fixable": true }
  ],
  "aide.convertLanguagePairs": {
    "vue": "vue"
  }
}
